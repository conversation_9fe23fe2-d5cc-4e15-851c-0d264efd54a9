# 命令行参数

命令行参数的介绍会分为基本参数，原子参数、集成参数和特定模型参数。命令行最终使用的参数列表为集成参数。集成参数继承自基本参数和一些原子参数。特定模型参数是针对于具体模型的参数，可以通过`--model_kwargs'`或者环境变量进行设置。Megatron-SWIFT命令行参数介绍可以在[Megatron-SWIFT训练文档](./Megatron-SWIFT训练.md)中找到。

提示：
- 命令行传入list使用空格隔开即可。例如：`--dataset <dataset_path1> <dataset_path2>`。
- 命令行传入dict使用json。例如：`--model_kwargs '{"fps_max_frames": 12}'`。
- 带🔥的参数为重要参数，刚熟悉ms-swift的用户可以先关注这些命令行参数。

## 基本参数

- 🔥tuner_backend: 可选为'peft'，'unsloth'。默认为'peft'。
- 🔥train_type: 可选为: 'lora'、'full'、'longlora'、'adalora'、'llamapro'、'adapter'、'vera'、'boft'、'fourierft'、'reft'。默认为'lora'。
- 🔥adapters: 用于指定adapter的id/path的list，默认为`[]`。
- external_plugins: 外部plugin py文件列表，这些文件会被注册进plugin模块中，例子请参见[这里](https://github.com/modelscope/ms-swift/tree/main/examples/train/grpo/plugin/run_external_reward_func.sh)。默认为`[]`。
- seed: 默认为42。
- model_kwargs: 特定模型可传入的额外参数，该参数列表会在训练推理时打印日志进行提示。例如`--model_kwargs '{"fps_max_frames": 12}'`。默认为None。
- load_args: 当指定`--resume_from_checkpoint`、`--model`、`--adapters`会读取保存文件中的`args.json`，读取的keys查看[base_args.py](https://github.com/modelscope/ms-swift/blob/main/swift/llm/argument/base_args/base_args.py)。推理和导出时默认为True，训练时默认为False。
- load_data_args: 如果将该参数设置为True，则会额外读取`args.json`中的数据参数。默认为False。
- use_hf: 控制模型下载、数据集下载、模型推送使用ModelScope还是HuggingFace。默认为False，使用ModelScope。
- hub_token: hub token. modelscope的hub token可以查看[这里](https://modelscope.cn/my/myaccesstoken)。默认为None。
- custom_register_path: 自定义模型、对话模板和数据集注册的`.py`文件路径的list。默认为`[]`。
- ddp_timeout: 默认为18000000，单位为秒。
- ddp_backend: 可选为"nccl"、"gloo"、"mpi"、"ccl"、"hccl" 、"cncl"、"mccl"。默认为None，进行自动选择。
- ignore_args_error: 用于兼容notebook。默认为False。

### 模型参数
- 🔥model: 模型id或模型本地路径。如果是自定义模型请配合`model_type`和`template`使用，具体可以参考[自定义模型](../Customization/自定义模型.md)。默认为None。
- model_type: 模型类型。相同的模型架构、template、模型加载过程被定义为一个model_type。默认为None，根据`--model`的后缀和config.json中的architectures属性进行自动选择。
- model_revision: 模型版本，默认为None。
- task_type: 默认为'causal_lm'。可选为'causal_lm'、'seq_cls'、'embedding'。seq_cls的例子可以查看[这里](https://github.com/modelscope/ms-swift/tree/main/examples/train/seq_cls)，embedding的例子查看[这里](https://github.com/modelscope/ms-swift/tree/main/examples/train/embedding)。
- 🔥torch_dtype: 模型权重的数据类型，支持`float16`,`bfloat16`,`float32`。默认为None，从config.json文件中读取。
- attn_impl: attention类型，可选项为'sdpa', 'eager', 'flash_attention_2', 'flash_attention_3'等。默认使用None，读取config.json。
  - 注意：这几种实现并不一定都支持，这取决于对应模型的支持情况。
  - 若设置为'flash_attn'（兼容旧版本），则使用'flash_attention_2'。
- new_special_tokens: 需要新增的特殊tokens。默认为`[]`。例子参考[这里](https://github.com/modelscope/ms-swift/tree/main/examples/train/new_special_tokens)。
  - 注意：你也可以传入以`.txt`结尾的文件路径，每行为一个special token。
- num_labels: 分类模型（即`--task_type seq_cls`）需要指定该参数。代表标签数量，默认为None。
- problem_type: 分类模型（即`--task_type seq_cls`）需要指定该参数。可选为'regression', 'single_label_classification', 'multi_label_classification'。默认为None，根据num_labels和数据集类型进行自动设置。
- rope_scaling: rope类型，支持`linear`和`dynamic`和`yarn`，或者直接传入一个json字符串:`"{\"factor\":2.0,\"type\":\"yarn\"}"`，请配合`max_model_len`共同使用。默认为None。
- max_model_len: 如果使用`rope_scaling`，可以设置`max_model_len`，该参数可以用来计算rope的`factor`倍数。最后的`max_position_embeddings`会设置为原值的`factor`倍。如果`rope_scaling`是json字符串，则本值不生效。
- device_map: 模型使用的device_map配置，例如：'auto'、'cpu'、json字符串、json文件路径。默认为None，根据设备和分布式训练情况自动设置。
- max_memory: device_map设置为'auto'或者'sequential'时，会根据max_memory进行模型权重的device分配，例如：`--max_memory '{0: "20GB", 1: "20GB"}'`。默认为None。
- local_repo_path: 部分模型在加载时依赖于github repo。为了避免`git clone`时遇到网络问题，可以直接使用本地repo。该参数需要传入本地repo的路径, 默认为`None`。
- init_strategy: 加载模型时，初始化模型中所有未初始化的参数。可选为'zero', 'uniform', 'normal', 'xavier_uniform', 'xavier_normal', 'kaiming_uniform', 'kaiming_normal', 'orthogonal'。默认为None。


### 数据参数
- 🔥dataset: 数据集id或路径的list。默认为`[]`。每个数据集的传入格式为：`数据集id or 数据集路径:子数据集#采样数量`，其中子数据集和取样数据可选。本地数据集支持jsonl、csv、json、文件夹等。开源数据集可以通过git clone到本地并将文件夹传入而离线使用。自定义数据集格式可以参考[自定义数据集](../Customization/自定义数据集.md)。你可以传入`--dataset <dataset1> <dataset2>`来使用多个数据集。
  - 子数据集: 该参数只有当dataset为ID或者文件夹时生效。若注册时指定了subsets，且只有一个子数据集，则默认选择注册时指定的子数据集，否则默认为'default'。你可以使用`/`来选择多个子数据集，例如：`<dataset_id>:subset1/subset2`。你也可以使用'all'来选择所有的子数据集，例如：`<dataset_id>:all`。
  - 采样数量: 默认使用完整的数据集。若采样数少于数据样本总数，则进行随机选择（不重复采样）。若采样数高于数据样本总数，则只额外随机采样`采样数%数据样本总数`的样本，数据样本重复采样`采样数//数据样本总数`次。注意：流式数据集只进行顺序采样。若设置`--dataset_shuffle false`，则非流式数据集也进行顺序采样。
- 🔥val_dataset: 验证集id或路径的list。默认为`[]`。
- 🔥split_dataset_ratio: 不指定val_dataset时从训练集拆分验证集的比例，默认为0.，即不从训练集切分验证集。
  - 注意：该参数在"ms-swift<3.6"的默认值为0.01。
- data_seed: 数据集随机种子，默认为42。
- 🔥dataset_num_proc: 数据集预处理的进程数，默认为1。
- 🔥load_from_cache_file: 是否从缓存中加载数据集，默认为True。
  - 注意：该参数在debug阶段建议设置为False。
- dataset_shuffle: 是否对dataset进行随机操作。默认为True。
  - 注意：CPT/SFT的随机包括两个部分：数据集的随机，由`dataset_shuffle`控制；train_dataloader中的随机，由`train_dataloader_shuffle`控制。
- val_dataset_shuffle: 是否对val_dataset进行随机操作。默认为False。
- streaming: 流式读取并处理数据集，默认False。
  - 注意：需要额外设置`--max_steps`，因为流式数据集无法获得其长度。你可以通过设置`--save_strategy epoch`并设置较大的max_steps来实现与`--num_train_epochs`等效的训练。或者，你也可以设置`max_epochs`确保训练到对应epochs时退出训练，并对权重进行验证和保存。
  - 注意：流式数据集可以跳过预处理等待，将预处理时间与训练时间重叠。流式数据集的预处理只在rank0上进行，并通过数据分发的方式同步到其他进程，其通常效率不如非流式数据集采用的数据分片读取方式。当训练的world_size较大时，预处理和数据分发将成为训练瓶颈。
- interleave_prob: 默认值为 None。在组合多个数据集时，默认使用 `concatenate_datasets` 函数；如果设置了该参数，则会使用 `interleave_datasets` 函数。该参数通常用于流式数据集的组合，并会作为参数传入 `interleave_datasets` 函数中。
- stopping_strategy: 可选为"first_exhausted", "all_exhausted"，默认为"first_exhausted"。传入interleave_datasets函数中。
- shuffle_buffer_size: 该参数用于指定流式数据集的随机buffer大小，默认为1000。该参数只在`dataset_shuffle`设置为true时有效。
- download_mode: 数据集下载模式，包含`reuse_dataset_if_exists`和`force_redownload`，默认为reuse_dataset_if_exists。
- columns: 用于对数据集进行列映射，使数据集满足AutoPreprocessor可以处理的样式，具体查看[这里](../Customization/自定义数据集.md)。你可以传入json字符串，例如：`'{"text1": "query", "text2": "response"}'`，代表将数据集中的"text1"映射为"query"，"text2"映射为"response"，而query-response格式可以被AutoPreprocessor处理。默认为None。
- strict: 如果为True，则数据集只要某行有问题直接抛错，否则会丢弃出错数据样本。默认False。
- 🔥remove_unused_columns: 是否删除数据集中不被使用的列，默认为True。
  - 若该参数设置为False，则将额外的数据集列传递至trainer的`compute_loss`函数内，方便自定义损失函数。
  - GPRO该参数的默认值为False。
- 🔥model_name: 仅用于自我认知任务，只对`swift/self-cognition`数据集生效，替换掉数据集中的`{{NAME}}`通配符。传入模型中文名和英文名，以空格分隔，例如：`--model_name 小黄 'Xiao Huang'`。默认为None。
- 🔥model_author: 仅用于自我认知任务，只对`swift/self-cognition`数据集生效，替换掉数据集中的`{{AUTHOR}}`通配符。传入模型作者的中文名和英文名，以空格分隔，例如：`--model_author '魔搭' 'ModelScope'`。默认为None。
- custom_dataset_info: 自定义数据集注册的json文件路径，参考[自定义数据集](../Customization/自定义数据集.md)。默认为`[]`。

### 模板参数
- 🔥template: 对话模板类型。默认为None，自动选择对应model的template类型。
- 🔥system: 自定义system字段，可以传入字符串或者txt文件路径。默认为None，使用template的默认system。
  - 注意：数据集中的system优先级最高，然后是`--system`，最后是定义在template中的`default_system`。
- 🔥max_length: 单样本的tokens最大长度。默认为None，设置为模型支持的tokens最大长度(max_model_len)。
  - 注意：PPO、GRPO和推理情况下，max_length代表max_prompt_length。
- truncation_strategy: 如果单样本的tokens超过`max_length`如何处理，支持`delete`, `left`和`right`，代表删除、左侧裁剪和右侧裁剪，默认为'delete'。
  - 暂不建议多模态模型的训练将truncation_strategy设置为`left`或`right`，这会导致图像token被裁减导致报错（待优化）。
- 🔥max_pixels: 多模态模型输入图片的最大像素数（H\*W），将超过该限制的图像进行缩放。默认为None，不限制最大像素数。
- 🔥agent_template: Agent模板，确定如何将工具列表转换成system，如何从模型回复中提取toolcall，以及确定`{"role": "tool_call", "content": "xxx"}`, `{"role": "tool_response", "content": "xxx"}`的模板格式。可选为"react_en", "hermes", "glm4", "qwen_en", "toolbench"等，更多请查看[这里](https://github.com/modelscope/ms-swift/blob/main/swift/plugin/agent_template/__init__.py)。默认为None，根据模型类型进行选择。
- norm_bbox: 控制如何缩放边界框（bbox）。选项为'norm1000'和'none'。'norm1000'表示将bbox坐标缩放至千分之一，而'none'表示不进行缩放。默认值为None，将根据模型自动选择。
- use_chat_template: 使用chat模板或generation模板。默认为`True`。
  - 注意：`swift pt`默认为False，使用generation模板。
- 🔥padding_free: 将一个batch中的数据进行展平而避免数据padding，从而降低显存占用并加快训练。默认为False。当前支持CPT/SFT/DPO/GRPO/GKD。
  - 注意：使用padding_free请结合`--attn_impl flash_attn`使用且"transformers>=4.44"，具体查看[该PR](https://github.com/huggingface/transformers/pull/31629)。（同packing）
  - 支持的多模态模型与多模态packing支持情况相同。相较于packing，padding_free不额外消耗时间和空间。注意：请使用"ms-swift>=3.6"，关注[此PR](https://github.com/modelscope/ms-swift/pull/4838)。
  - Megatron-SWIFT默认使用padding_free，即`qkv_format='thd'`，不需要额外设置。
- padding_side: 当训练`batch_size>=2`时的padding_side，可选值为'left'、'right'，默认为'right'。（推理时的batch_size>=2时，只进行左padding）。
  - 注意：PPO和GKD默认设置为'left'。
- loss_scale: 训练tokens的loss权重设置。默认为`'default'`，代表所有response（含history）以1计算交叉熵损失，忽略对应agent_template的`tool_response`的损失。可选值为'default'、'last_round'、'all'、'ignore_empty_think'，以及agent需要的loss_scale: 'react'、'hermes'、'qwen'、'agentflan'、'alpha_umi'。agent部分可以查看[插件化](../Customization/插件化.md)和[Agent文档](./Agent支持.md)。
  - 'last_round': 只计算最后一轮response的损失。
  - 'all': 计算所有tokens的损失。
  - 'ignore_empty_think': 在`'default'`的基础上，忽略空的`'<think>\n\n</think>\n\n'`损失计算，具体请参考[此issue](https://github.com/modelscope/ms-swift/issues/4030)。
  - 'react', 'hermes', 'qwen': 在`'default'`的基础上，将`tool_call`部分的loss权重调整为2。
- sequence_parallel_size: 序列并行大小，默认是1。当前支持CPT/SFT/DPO/GRPO。训练脚本参考[这里](https://github.com/modelscope/ms-swift/tree/main/examples/train/sequence_parallel/ulysses/sequence_parallel.sh)。
- response_prefix: response的前缀字符，例如QwQ-32B将response_prefix设置为`'<think>\n'`。默认为None，根据模型自动设置。
  - 注意：若对deepseek-r1/qwq模型使用不包含`<think>...</think>`的数据集进行训练，请加在推理训练后模型时额外传入`--response_prefix ''`。
- template_backend: 选择template后端，可选为'swift'、'jinja'，默认为'swift'。如果使用jinja，则使用transformers的`apply_chat_template`。
  - 注意：jinja的template后端只支持推理，不支持训练。

### 生成参数
参考[generation_config](https://huggingface.co/docs/transformers/main_classes/text_generation#transformers.GenerationConfig)文档。

- 🔥max_new_tokens: 推理最大生成新tokens的数量。默认为None，无限制。
- temperature: 温度参数。默认为None，读取generation_config.json。
  - 注意：do_sample参数在本版本中移除了，请将temperature配置为0来达到相同效果。
- top_k: top_k参数，默认为None。读取generation_config.json。
- top_p: top_p参数，默认为None。读取generation_config.json。
- repetition_penalty: 重复惩罚项。默认为None，读取generation_config.json。
- num_beams: beam search的并行保留数量，默认为1。
- 🔥stream: 流式输出，默认为`None`，即使用交互式界面时为True，数据集批量推理时为False。
  - "ms-swift<3.6"stream默认值为False。
- stop_words: 除了eos_token外额外的停止词，默认为`[]`。
  - 注意：eos_token会在输出respsone中被删除，额外停止词会在输出中保留。
- logprobs: 是否输出logprobs，默认为False。
- top_logprobs: 输出top_logprobs的数量，默认为None。

### 量化参数
以下为加载模型时量化的参数，具体含义可以查看[量化](https://huggingface.co/docs/transformers/main/en/main_classes/quantization)文档。这里不包含`swift export`中涉及的`gptq`、`awq`量化参数。

- 🔥quant_method: 加载模型时采用的量化方法，可选项为'bnb'、'hqq'、'eetq'、'quanto'和'fp8'，默认为None。
- 🔥quant_bits: 量化bits数，默认为None。
- hqq_axis: hqq量化axis，默认为None。
- bnb_4bit_compute_dtype: bnb量化计算类型，可选为`float16`、`bfloat16`、`float32`。默认为None，设置为`torch_dtype`。
- bnb_4bit_quant_type: bnb量化类型，支持`fp4`和`nf4`，默认为`nf4`。
- bnb_4bit_use_double_quant: 是否使用双重量化，默认为`True`。
- bnb_4bit_quant_storage: bnb量化存储类型，默认为None。


## 原子参数

### Seq2SeqTrainer参数

该参数列表继承自transformers `Seq2SeqTrainingArguments`，ms-swift对其默认值进行了覆盖。未列出的请参考[HF官方文档](https://huggingface.co/docs/transformers/main/en/main_classes/trainer#transformers.Seq2SeqTrainingArguments)。

- 🔥output_dir: 默认为None，设置为`output/<model_name>`。
- 🔥gradient_checkpointing: 是否使用gradient_checkpointing，默认为True。
- 🔥vit_gradient_checkpointing: 多模态模型训练时，是否对vit部分开启gradient_checkpointing。默认为None，即设置为`gradient_checkpointing`。例子参考[这里](https://github.com/modelscope/ms-swift/blob/main/examples/train/multimodal/vit_gradient_checkpointing.sh)。
  - 注意：多模态模型且是LoRA训练时，当设置了`--freeze_vit false`，且命令行中出现以下警告：`UserWarning: None of the inputs have requires_grad=True. Gradients will be None`，请设置`--vit_gradient_checkpointing false`，或提相关issue。全参数训练则不会出现该问题。
- 🔥deepspeed: 默认为None。可以设置为'zero0', 'zero1', 'zero2', 'zero3', 'zero2_offload', 'zero3_offload'来使用ms-swift内置的deepspeed配置文件。
- zero_hpz_partition_size: 默认为None，这个参数是ZeRO++的特性，即node内模型分片，node间数据分片，如果遇到grad_norm NaN，请尝试使用`--torch_dtype float16`。
- 🔥per_device_train_batch_size: 默认值1。
- 🔥per_device_eval_batch_size: 默认值1。
- 🔥gradient_accumulation_steps: 梯度累加，默认为None，即设置gradient_accumulation_steps使得total_batch_size>=16。total_batch_size等于`per_device_train_batch_size * gradient_accumulation_steps * world_size`。
- weight_decay: weight衰减系数，默认值0.1。
- adam_beta2: 默认为0.95。
- 🔥learning_rate: 学习率，全参数默认为1e-5，LoRA等tuners为1e-4。
- 🔥vit_lr: 当训练多模态大模型时，该参数指定vit的学习率，默认为None，等于learning_rate。
  - 通常与`--freeze_vit`、`--freeze_aligner`参数结合使用。
- 🔥aligner_lr: 当训练多模态大模型时，该参数指定aligner的学习率，默认为None，等于learning_rate。
- lr_scheduler_type: lr_scheduler类型，默认为'cosine'。
- lr_scheduler_kwargs: lr_scheduler其他参数。默认为None。
- 🔥gradient_checkpointing_kwargs: 传入`torch.utils.checkpoint`中的参数。例如设置为`--gradient_checkpointing_kwargs '{"use_reentrant": false}'`。默认为None。
  - 注意：当使用DDP而不使用deepspeed/fsdp，且gradient_checkpointing_kwargs为None，会默认设置其为`'{"use_reentrant": false}'`。
- full_determinism: 确保训练中获得可重现的结果，注意：这会对性能产生负面影响。默认为False。
- 🔥report_to: 默认值为`tensorboard`。你也可以指定`--report_to tensorboard wandb swanlab`、`--report_to all`。
- logging_first_step: 是否记录第一个step的日志，默认为True。
- logging_steps: 日志打印间隔，默认为5。
- router_aux_loss_coef: 用于moe模型训练时，设置 aux_loss 的权重。默认为None，使用config中值。若设置为0，则不计算 aux_loss。
- logging_dir: tensorboard日志路径。默认为None，即设置为`f'{self.output_dir}/runs'`。
- predict_with_generate: 验证时使用生成式的方式，默认为False。
- metric_for_best_model: 默认为None，即当`predict_with_generate`设置为False时，设置为'loss'，否则设置为'rouge-l'（在PPO训练时，不进行默认值设置；GRPO训练设置为'reward'）。
- greater_is_better: 默认为None，即当`metric_for_best_model`含'loss'时，设置为False，否则设置为True。
- max_epochs: 训练到`max_epochs`时强制退出训练，并对权重进行验证和保存。该参数在使用流式数据集时很有用。默认为None。

其他重要参数：
- 🔥num_train_epochs: 训练的epoch数，默认为3。
- 🔥save_strategy: 保存模型的策略，可选为'no'、'steps'、'epoch'，默认为'steps'。
- 🔥save_steps: 默认为500。
- 🔥eval_strategy: 评估策略。默认为None，跟随`save_strategy`的策略。
  - 若不使用`val_dataset`和`eval_dataset`且`split_dataset_ratio`为0，则默认为'no'。
- 🔥eval_steps: 默认为None，如果存在评估数据集，则跟随`save_steps`的策略。
- 🔥save_total_limit: 最多保存的checkpoint数，会将过期的checkpoint进行删除。默认为None，保存所有的checkpoint。
- max_steps: 最大训练的steps数。在数据集为流式时需要被设置。默认为-1。
- 🔥warmup_ratio: 默认为0.。
- save_on_each_node: 默认为False。在多机训练时需要被考虑。
- save_only_model: 是否只保存模型权重而不包含优化器状态，随机种子状态等内容。默认为False。
- 🔥resume_from_checkpoint: 断点续训参数，传入checkpoint路径。默认为None。
  - 贴士：断点续训请保持其他参数不变，额外增加`--resume_from_checkpoint checkpoint_dir`。权重等信息将在trainer中读取。
  - 注意: resume_from_checkpoint会读取模型权重，优化器权重，随机种子，并从上次训练的steps继续开始训练。你可以指定`--resume_only_model`只读取模型权重。
- resume_only_model: 默认为False。如果在指定resume_from_checkpoint的基础上，将该参数设置为True，则仅resume模型权重，而忽略优化器权重和随机种子。
  - 注意：在"ms-swift>=3.7"，resume_only_model默认将进行数据跳过，并使用`ignore_data_skip`参数进行控制。若要恢复"ms-swift<3.7"的行为，请设置`--ignore_data_skip true`。
- ignore_data_skip: 当设置`resume_from_checkpoint`和`resume_only_model`时，该参数控制是否跳过已经训练的数据，并将epoch和迭代数等训练状态进行恢复。默认为False。若设置为True，则将不加载训练状态并不进行数据跳过，将从迭代数0开始训练。
- 🔥ddp_find_unused_parameters: 默认为None。
- 🔥dataloader_num_workers: 默认为None，若是windows平台，则设置为0，否则设置为1。
- dataloader_pin_memory: 默认为True。
- dataloader_persistent_workers: 默认为False。
- dataloader_prefetch_factor: 默认为None，若`dataloader_num_workers > 0`，设置为10。
- train_dataloader_shuffle: CPT/SFT训练的dataloader是否随机，默认为True。该参数对IterableDataset无效。IterableDataset采用顺序的方式读取。
- 🔥neftune_noise_alpha: neftune添加的噪声系数, 默认为0，通常可以设置为5、10、15。
- 🔥use_liger_kernel: 是否启用[Liger](https://github.com/linkedin/Liger-Kernel)内核加速训练并减少显存消耗。默认为False。示例shell参考[这里](https://github.com/modelscope/ms-swift/blob/main/examples/train/liger)。
  - 注意：liger_kernel不支持device_map，请使用DDP/DeepSpeed进行多卡训练。
- average_tokens_across_devices: 是否在设备之间进行token数平均。如果设置为True，将使用all_reduce同步`num_tokens_in_batch`以进行精确的损失计算。默认为False。
- max_grad_norm: 梯度裁剪。默认为1.。
- push_to_hub: 推送checkpoint到hub。默认为False。
- hub_model_id: 默认为None。
- hub_private_repo: 默认为False。

### Tuner参数
- 🔥freeze_llm: 该参数只对多模态模型生效，可用于全参和LoRA，但含义不同。若是全参数训练，将freeze_llm设置为True将会将llm部分权重进行冻结，若是LoRA训练且`target_modules`设置为'all-linear'，将freeze_llm设置为True将会取消在llm部分添加LoRA模块。该参数默认为False。
- 🔥freeze_vit: 该参数只对多模态模型生效，可用于全参和LoRA，含义参考`freeze_llm`。默认为True。
  - 注意：这里的vit不仅限于vision_tower, 也包括audio_tower。
- 🔥freeze_aligner: 该参数只对多模态模型生效，可用于全参和LoRA，含义参考`freeze_llm`。默认为True。
- 🔥target_modules: 指定lora模块, 默认为`['all-linear']`。在LLM和多模态LLM中，其行为有所不同. 若是LLM则自动寻找除lm_head外的linear并附加tuner，若是多模态LLM，则默认只在LLM上附加tuner，该行为可以被`freeze_llm`、`freeze_vit`、`freeze_aligner`控制。该参数不限于LoRA，可用于其他tuners。
- 🔥target_regex: 指定lora模块的regex表达式，默认为`None`。如果该值传入，则target_modules参数失效。该参数不限于LoRA，可用于其他tuners。
- init_weights: 初始化weights的方法，LoRA可以指定为`true`、`false`、`gaussian`、`pissa`、`pissa_niter_[number of iters]`，Bone可以指定为`true`、`false`、`bat`。默认值`true`。
- 🔥modules_to_save: 在已附加tuner后，额外指定一部分原模型模块参与训练和存储。默认为`[]`。该参数不限于LoRA，可用于其他tuners。

#### 全参
- freeze_parameters: 需要被冻结参数的前缀，默认为`[]`。
- freeze_parameters_regex: 需要被冻结参数的正则表达式，默认为None。
- freeze_parameters_ratio: 从下往上冻结的参数比例，默认为0。可设置为1将所有参数冻结，结合`trainable_parameters`设置可训练参数。
- trainable_parameters: 额外可训练参数的前缀，默认为`[]`。
- trainable_parameters_regex: 匹配额外可训练参数的正则表达式，默认为None。
  - 备注：`trainable_parameters`、`trainable_parameters_regex`的优先级高于`freeze_parameters`、`freeze_parameters_regex`和`freeze_parameters_ratio`。当指定全参数训练时，会将所有模块设置为可训练的状态，随后根据`freeze_parameters`、`freeze_parameters_regex`、`freeze_parameters_ratio`将部分参数冻结，最后根据`trainable_parameters`、`trainable_parameters_regex`重新打开部分参数参与训练。

#### LoRA
- 🔥lora_rank: 默认为`8`。
- 🔥lora_alpha: 默认为`32`。
- lora_dropout: 默认为`0.05`。
- lora_bias: 默认为`'none'`，可以选择的值: 'none'、'all'。如果你要将bias全都设置为可训练，你可以设置为`'all'`。
- lora_dtype: 指定lora模块的dtype类型。支持'float16'、'bfloat16'、'float32'。默认为None，跟随原模型类型。
- 🔥use_dora: 默认为`False`，是否使用`DoRA`。
- use_rslora: 默认为`False`，是否使用`RS-LoRA`。
- 🔥lorap_lr_ratio: LoRA+参数，默认值`None`，建议值`10~16`。使用lora时指定该参数可使用lora+。

##### LoRA-GA
- lora_ga_batch_size: 默认值为 `2`。在 LoRA-GA 中估计梯度以进行初始化时使用的批处理大小。
- lora_ga_iters: 默认值为 `2`。在 LoRA-GA 中估计梯度以进行初始化时的迭代次数。
- lora_ga_max_length: 默认值为 `1024`。在 LoRA-GA 中估计梯度以进行初始化时的最大输入长度。
- lora_ga_direction: 默认值为 `ArB2r`。在 LoRA-GA 中使用估计梯度进行初始化时的初始方向。允许的值有：`ArBr`、`A2rBr`、`ArB2r` 和 `random`。
- lora_ga_scale: 默认值为 `stable`。LoRA-GA 的初始化缩放方式。允许的值有：`gd`、`unit`、`stable` 和 `weightS`。
- lora_ga_stable_gamma: 默认值为 `16`。当初始化时选择 `stable` 缩放时的 gamma 值。

#### FourierFt

FourierFt使用`target_modules`, `target_regex`, `modules_to_save`三个参数.

- fourier_n_frequency: 傅里叶变换的频率数量, `int`类型, 类似于LoRA中的`r`. 默认值`2000`.
- fourier_scaling: W矩阵的缩放值, `float`类型, 类似LoRA中的`lora_alpha`. 默认值`300.0`.

#### BOFT

BOFT使用`target_modules`, `target_regex`, `modules_to_save`三个参数.

- boft_block_size: BOFT块尺寸, 默认值4.
- boft_block_num: BOFT块数量, 不能和`boft_block_size`同时使用.
- boft_dropout: boft的dropout值, 默认0.0.

#### Vera

Vera使用`target_modules`, `target_regex`, `modules_to_save`三个参数.

- vera_rank: Vera Attention的尺寸, 默认值256.
- vera_projection_prng_key: 是否存储Vera映射矩阵, 默认为True.
- vera_dropout: Vera的dropout值, 默认`0.0`.
- vera_d_initial: Vera的d矩阵的初始值, 默认`0.1`.

#### GaLore

- 🔥use_galore: 默认值False, 是否使用GaLore.
- galore_target_modules: 默认值None, 不传的情况下对attention和mlp应用GaLore.
- galore_rank: 默认值128, GaLore的rank值.
- galore_update_proj_gap: 默认值50, 分解矩阵的更新间隔.
- galore_scale: 默认值1.0, 矩阵权重系数.
- galore_proj_type: 默认值`std`, GaLore矩阵分解类型.
- galore_optim_per_parameter: 默认值False, 是否给每个Galore目标Parameter设定一个单独的optimizer.
- galore_with_embedding: 默认值False, 是否对embedding应用GaLore.
- galore_quantization: 是否使用q-galore. 默认值`False`.
- galore_proj_quant: 是否对SVD分解矩阵做量化, 默认`False`.
- galore_proj_bits: SVD量化bit数.
- galore_proj_group_size: SVD量化分组数.
- galore_cos_threshold: 投影矩阵更新的cos相似度阈值. 默认值0.4.
- galore_gamma_proj: 在投影矩阵逐渐相似后会拉长更新间隔, 本参数为每次拉长间隔的系数, 默认值2.
- galore_queue_size: 计算投影矩阵相似度的队列长度, 默认值5.

#### LISA

注意:LISA仅支持全参数，即`--train_type full`.

- 🔥lisa_activated_layers: 默认值`0`, 代表不使用LISA，改为非0代表需要激活的layers个数，建议设置为2或8.
- lisa_step_interval: 默认值`20`, 多少iter切换可反向传播的layers.

#### UNSLOTH

🔥unsloth无新增参数，对已有参数进行调节即可支持:

```
--tuner_backend unsloth
--train_type full/lora
--quant_bits 4
```

#### LLAMAPRO

- 🔥llamapro_num_new_blocks: 默认值`4`, 插入的新layers总数.
- llamapro_num_groups: 默认值`None`, 分为多少组插入new_blocks, 如果为`None`则等于`llamapro_num_new_blocks`, 即每个新的layer单独插入原模型.

#### AdaLoRA

以下参数`train_type`设置为`adalora`时生效. adalora的`target_modules`等参数继承于lora的对应参数, 但`lora_dtype`参数不生效.

- adalora_target_r: 默认值`8`, adalora的平均rank.
- adalora_init_r: 默认值`12`, adalora的初始rank.
- adalora_tinit: 默认值`0`, adalora的初始warmup.
- adalora_tfinal: 默认值`0`, adalora的final warmup.
- adalora_deltaT: 默认值`1`, adalora的step间隔.
- adalora_beta1: 默认值`0.85`, adalora的EMA参数.
- adalora_beta2: 默认值`0.85`, adalora的EMA参数.
- adalora_orth_reg_weight: 默认值`0.5`, adalora的正则化参数.

#### ReFT

以下参数`train_type`设置为`reft`时生效.

> 1. ReFT无法合并tuner
> 2. ReFT和gradient_checkpointing不兼容
> 3. 如果使用DeepSpeed遇到问题请暂时卸载DeepSpeed

- 🔥reft_layers: ReFT应用于哪些层上, 默认为`None`, 代表所有层, 可以输入层号的list, 例如reft_layers 1 2 3 4`
- 🔥reft_rank: ReFT矩阵的rank, 默认为`4`.
- reft_intervention_type: ReFT的类型, 支持'NoreftIntervention', 'LoreftIntervention', 'ConsreftIntervention', 'LobireftIntervention', 'DireftIntervention', 'NodireftIntervention', 默认为`LoreftIntervention`.
- reft_args: ReFT Intervention中的其他支持参数, 以json-string格式输入.

### vLLM参数
参数含义可以查看[vllm文档](https://docs.vllm.ai/en/latest/serving/engine_args.html)。

- 🔥vllm_gpu_memory_utilization: GPU内存比例，取值范围为0到1。默认值`0.9`。
  - 注意：该参数在"ms-swift<3.7"的参数名为`gpu_memory_utilization`。下面的`vllm_`参数同理。若出现参数不匹配问题，请查看[ms-swift3.6文档](https://swift.readthedocs.io/zh-cn/v3.6/Instruction/%E5%91%BD%E4%BB%A4%E8%A1%8C%E5%8F%82%E6%95%B0.html#vllm)。
- 🔥vllm_tensor_parallel_size: tp并行数，默认为`1`。
- vllm_pipeline_parallel_size: pp并行数，默认为`1`。
- vllm_data_parallel_size: dp并行数，默认为`1`，在`rollout`命令中生效。
- vllm_enable_expert_parallel: 开启专家并形，默认为False。
- vllm_max_num_seqs: 单次迭代中处理的最大序列数，默认为`256`。
- 🔥vllm_max_model_len: 默认为`None`，即从config.json中读取。
- vllm_disable_custom_all_reduce: 禁用自定义的 all-reduce 内核，回退到 NCCL。为了稳定性，默认为`True`。
- vllm_enforce_eager: vllm使用pytorch eager模式还是建立cuda graph，默认为`False`。设置为True可以节约显存，但会影响效率。
- 🔥vllm_limit_mm_per_prompt: 控制vllm使用多图，默认为`None`。例如传入`--vllm_limit_mm_per_prompt '{"image": 5, "video": 2}'`。
- vllm_max_lora_rank: 默认为`16`。vllm对于lora支持的参数。
- vllm_quantization: vllm可以在内部量化模型，参数支持的值详见[这里](https://docs.vllm.ai/en/latest/serving/engine_args.html)。
- vllm_enable_prefix_caching: 开启vllm的自动前缀缓存，节约重复查询前缀的处理时间。默认为`False`。
- vllm_use_async_engine: vLLM backend下是否使用async engine。部署情况（swift deploy）默认为True，其他情况默认为False。

### SGLang参数
参数含义可以查看[sglang文档](https://docs.sglang.ai/backend/server_arguments.html)。

- sglang_tp_size: tp数。默认为1。
- sglang_pp_size: pp数。默认为1。
- sglang_dp_size: dp数。默认为1。
- sglang_ep_size: ep数。默认为1。
- sglang_enable_ep_moe: 是否启用ep moe。默认为False。
- sglang_mem_fraction_static: 用于静态分配模型权重和KV缓存内存池的GPU内存比例。如果你遇到GPU内存不足错误，可以尝试降低该值。默认为None。
- sglang_context_length: 模型的最大上下文长度。默认为 None，将使用模型的`config.json`中的值。
- sglang_disable_cuda_graph: 禁用CUDA图。默认为False。
- sglang_quantization: 量化方法。默认为None。
- sglang_kv_cache_dtype: 用于k/v缓存存储的数据类型。'auto'表示将使用模型的数据类型。'fp8_e5m2'和'fp8_e4m3'适用于CUDA 11.8及以上版本。默认为'auto'。
- sglang_enable_dp_attention: 为注意力机制启用数据并行，为前馈网络（FFN）启用张量并行。数据并行的规模（dp size）应等于张量并行的规模（tp size）。目前支持DeepSeek-V2/3以及Qwen2/3 MoE模型。默认为False。
- sglang_disable_custom_all_reduce: 禁用自定义的 all-reduce 内核，回退到 NCCL。为了稳定性，默认为True。

### LMDeploy参数
参数含义可以查看[lmdeploy文档](https://lmdeploy.readthedocs.io/en/latest/api/pipeline.html#turbomindengineconfig)。

- 🔥lmdeploy_tp: tensor并行度。默认为`1`。
- lmdeploy_session_len: 最大会话长度。默认为`None`。
- lmdeploy_cache_max_entry_count: k/v缓存占用的GPU内存百分比。默认为`0.8`。
- lmdeploy_quant_policy: 默认为0。当需要将k/v量化为4或8位时，分别将其设置为4或8。
- lmdeploy_vision_batch_size: 传入VisionConfig的max_batch_size参数。默认为`1`。

### 合并参数

- 🔥merge_lora: 是否合并lora，本参数支持lora、llamapro、longlora，默认为False。例子参数[这里](https://github.com/modelscope/ms-swift/blob/main/examples/export/merge_lora.sh)。
- safe_serialization: 是否存储safetensors，默认为True。
- max_shard_size: 单存储文件最大大小，默认'5GB'。


## 集成参数

### 训练参数
训练参数除包含[基本参数](#基本参数)、[Seq2SeqTrainer参数](#Seq2SeqTrainer参数)、[tuner参数](#tuner参数)外，还包含下面的部分:

- add_version: 在output_dir上额外增加目录`'<版本号>-<时间戳>'`防止权重覆盖，默认为True。
- check_model: 检查本地模型文件有损坏或修改并给出提示，默认为True。如果是断网环境，请设置为False。
- 🔥create_checkpoint_symlink: 额外创建checkpoint软链接，方便书写自动化训练脚本。best_model和last_model的软链接路径分别为f'{output_dir}/best'和f'{output_dir}/last'。
- loss_type: loss类型。默认为None，使用模型自带损失函数。
- channels: 数据集包含的channel集合。默认为None。结合`--loss_type channel_loss`使用，可参考[这里](https://github.com/modelscope/ms-swift/blob/main/examples/train/plugins/channel_loss.sh)。
- 🔥packing: 是否使用序列packing提升计算效率，默认为False。当前支持`swift pt/sft`。
  - 注意：使用packing请结合`--attn_impl flash_attn`使用且"transformers>=4.44"，具体查看[该PR](https://github.com/huggingface/transformers/pull/31629)。
  - 支持的多模态模型参考：https://github.com/modelscope/ms-swift/blob/main/examples/train/packing/qwen2_5_vl.sh。注意：请使用"ms-swift>=3.6"，关注[此PR](https://github.com/modelscope/ms-swift/pull/4838)。
- packing_cache: 指定 packing 缓存目录。默认值为`None`，表示缓存将存储在环境变量 `$MODELSCOPE_CACHE`所指定的路径下。在跨节点使用 packing 功能时，需确保所有节点的 packing 缓存路径共享且一致。你可以通过设置`MODELSCOPE_CACHE`环境变量，或在命令行中添加 `--packing_cache <shared_path>`参数来实现这一要求。
  - 注意：该参数将在"ms-swift>=3.7"被移除。多机packing不再需要设置packing_cache。
- lazy_tokenize: 是否使用lazy_tokenize。若该参数设置为False，则在训练之前对所有的数据集样本进行tokenize（多模态模型则包括从磁盘中读取图片）。该参数在LLM训练中默认设置为False，而MLLM训练默认为True，节约内存。
- 🔥cached_dataset: 训练中使用缓存数据集（使用`swift export --to_cached_dataset true ...`命令产生），避免大型数据集训练时，tokenize占用gpu时。默认为`[]`。
  - 注意：cached_dataset支持`--packing`，但不支持`--lazy_tokenize`和`--streaming`。
- use_logits_to_keep: 通过在`forward`中根据labels传入logits_to_keep，减少无效logits的计算与存储，从而减少显存占用并加快训练速度。默认为None，进行自动选择。
  - 注意：为了稳定性，多模态模型该值默认为False，需要手动设置。
- acc_strategy: 训练和验证时计算acc的策略。可选为`seq`和`token`级别的acc，默认为`token`。
- max_new_tokens: 覆盖生成参数。predict_with_generate=True时的最大生成token数量，默认64。
- temperature: 覆盖生成参数。predict_with_generate=True时的temperature，默认0。
- optimizer: plugin的自定义optimizer名称，默认为None。可选optimizer参考[这里](https://github.com/modelscope/ms-swift/blob/main/swift/plugin/optimizer.py)。
- metric: plugin的自定义metric名称。默认为None，即在predict_with_generate=False的情况下设置为'acc'，在predict_with_generate=True的情况下设置为'nlg'。
- eval_use_evalscope: 是否使用evalscope进行训练时评测，需要设置该参数来开启评测，具体使用参考[示例](../Instruction/评测.md#训练中评测)。
- eval_dataset: 评测数据集，可设置多个数据集，用空格分割。
- eval_dataset_args: 评测数据集参数，json格式，可设置多个数据集的参数。
- eval_limit: 评测数据集采样数。
- eval_generation_config: 评测时模型推理配置，json格式，默认为`{'max_tokens': 512}`。

#### SWANLAB

- swanlab_token: SwanLab的api-key。
- swanlab_project: swanlab的project，需要在页面中预先创建好:[https://swanlab.cn/space/~](https://swanlab.cn/space/~)。
- swanlab_workspace: 默认为None，会使用api-key对应的username。
- swanlab_exp_name: 实验名，可以为空，为空时默认传入--output_dir的值。
- swanlab_lark_webhook_url: 默认为None。swanlab的lark webhook url，用于推送实验结果到飞书。
- swanlab_lark_secret: 默认为None。swanlab的lark secret，用于推送实验结果到飞书。
- swanlab_mode: 可选cloud和local，云模式或者本地模式。


### RLHF参数
RLHF参数继承于[训练参数](#训练参数)。

- 🔥rlhf_type: 人类对齐算法类型，支持'dpo'、'orpo'、'simpo'、'kto'、'cpo'、'rm'、'ppo'、'grpo'和'gkd'。默认为'dpo'。
- ref_model: 采用dpo、kto、ppo、grpo算法且使用全参数训练时需要传入。默认为None。
- ref_model_type: 同model_type。默认为None。
- ref_model_revision: 同model_revision。默认为None。
- 🔥beta: KL正则项系数，默认为`None`，即`simpo`算法默认为`2.`，GRPO默认为`0.04`，GKD默认为0.5，其他算法默认为`0.1`。具体参考[文档](./人类对齐.md)。
- label_smoothing: 是否使用DPO smoothing，默认值为`0`。
- max_completion_length: GRPO/PPO/GKD算法中的最大生成长度，默认为512。
- 🔥rpo_alpha: 控制DPO中加入sft_loss的权重，默认为`1`。最后的loss为`KL_loss + rpo_alpha * sft_loss`。
- loss_type: 损失类型
  - DPO: 可选项参考[文档](https://huggingface.co/docs/trl/main/en/dpo_trainer#loss-functions)，支持传入多个值实现混合训练([MPO](https://arxiv.org/abs/2411.10442)), 传入多个值时需要设置参数 loss_weights。默认为`sigmoid`
  - GRPO: 参考[GRPO参数](#grpo参数)
- loss_weights：在 DPO 训练中设置多个 loss_type 时，用于指定各个损失项的权重。
- cpo_alpha: CPO/SimPO loss 中 nll loss的系数, 默认为`1.`。
- simpo_gamma: SimPO算法中的reward margin项，论文建议设置为0.5-1.5，默认为`1.`。
- desirable_weight: KTO算法中对desirable response的loss权重 $\lambda_D$，默认为`1.`。
- undesirable_weight: KTO算法中对undesirable response的loss权重 $\lambda_U$，默认为`1.`。
- loss_scale: 覆盖模板参数，默认为'last_round'。
- temperature: 默认为0.9，该参数将在PPO、GRPO、GKD中使用。
- lmbda: 默认为0.5。该参数在GKD中使用。控制学生数据比例的 lambda 参数（即策略内学生生成输出所占的比例）。若lmbda为0，则不使用学生生成数据。
- sft_alpha: 默认为0。控制GKD中加入sft_loss的权重。最后的loss为`gkd_loss + sft_alpha * sft_loss`。
- seq_kd: 默认为False。该参数在GKD中使用。控制是否执行序列级知识蒸馏（Sequence-Level KD）的 seq_kd 参数（可视为对教师模型生成输出的监督式微调）。
  - 注意：你可以提前对数据集内容使用teacher模型进行推理（使用vllm/sglang/lmdeploy等推理引擎加速），并在训练时将`seq_kd`设置为False。或者将`seq_kd`设置为True，在训练时使用teacher模型生成序列（能保证多个epoch生成数据的不同，但效率较慢）。

#### Reward/Teacher模型参数
reward模型参数将在PPO、GRPO中使用。

- reward_model: 默认为None。
- reward_adapters: 默认为`[]`。
- reward_model_type: 默认为None。
- reward_model_revision: 默认为None。
- teather_model: 默认为None。rlhf_type为'gkd'时需传入此参数。
- teather_adapters: 默认为`[]`。
- teather_model_type: 默认为None。
- teather_model_revision: 默认为None。

#### PPO参数

以下参数含义可以参考[这里](https://huggingface.co/docs/trl/main/ppo_trainer)。
- num_ppo_epochs: 默认为4。
- whiten_rewards: 默认为False。
- kl_coef: 默认为0.05。
- cliprange: 默认为0.2。
- vf_coef: 默认为0.1。
- cliprange_value: 默认为0.2。
- gamma: 默认为1.0。
- lam: 默认为0.95。
- num_mini_batches: 默认为1。
- local_rollout_forward_batch_size: 默认为64。
- num_sample_generations: 默认为10。
- missing_eos_penalty: 默认为None。


#### GRPO参数
- per_device_train_batch_size: 每个设备训练批量大小，在GRPO中，指 completion 的批次大小。
- per_device_eval_batch_size: 每个设备评估批量大小，在GRPO中，指 completion 的批次大小。
- generation_batch_size: 采样completion批量大小，需要是 num_processes * per_device_train_batch_size 的倍数，默认等于 per_device_batch_size * gradient_accumulation_steps * num_processes
- steps_per_generation: 每轮生成的优化步数，默认等于gradient_accumulation_steps。与generation_batch_size 只能同时设置一个
- num_generations: 每个prompt采样的数量，论文中的G值，采样批量大小(generation_batch_size 或 steps_per_generation × per_device_batch_size × num_processes) 必须能被 num_generations 整除。默认为 8。
- ds3_gather_for_generation: 该参数适用于DeepSpeed ZeRO-3。如果启用，策略模型权重将被收集用于生成，从而提高生成速度。然而，禁用此选项允许训练超出单个GPU VRAM的模型，尽管生成速度会变慢。禁用此选项与vLLM生成不兼容。默认为True。
- reward_funcs: GRPO算法奖励函数，可选项为`accuracy`、`format`、`cosine`、`repetition`和`soft_overlong`，见swift/plugin/orm.py。你也可以在plugin中自定义自己的奖励函数。默认为`[]`。
- reward_weights: 每个奖励函数的权重。必须与奖励函数和奖励模型的总数量匹配。如果为 None，则所有奖励的权重都相等，为`1.0`。
  - 提示：如果GRPO训练中包含`--reward_model`，则其加在奖励函数的最后位置。
- reward_model_plugin: 奖励模型逻辑，默认为orm逻辑, 详细见[自定义奖励模型](./GRPO/DeveloperGuide/奖励模型.md#自定义奖励模型)。
- dataset_shuffle: 是否对dataset进行随机操作，默认为True。
- truncation_strategy: 对输入长度超过 `max_length`的处理方式，支持`delete`和`left`，代表删除、左侧裁剪，默认为`left`, 注意对于多模态模型，
左裁剪可能会裁剪掉多模态token导致模型前向报错shape mismatch。使用`delete`方式，对于超长数据会在原数据集中重采样其他数据作为补充。
- loss_type: loss 归一化的类型，可选项为['grpo', 'bnpo', 'dr_grpo'], 默认为'grpo', 具体查看该[pr](https://github.com/huggingface/trl/pull/3256#discussion_r2033213348)。
- log_completions: 是否记录训练中的模型生成内容，搭配 `--report_to wandb` 使用。默认为False。
  - 提示：若没有设置`--report_to wandb`，则会在checkpoint中创建`completions.jsonl`来存储生成内容。
- use_vllm: 是否使用 vLLM 作为 GRPO 生成的 infer_backend，默认为False。
- vllm_mode: vLLM 集成模式，可选项为 `server` 和 `colocate`。server 模式使用 `swift rollout` 拉起的 vLLM 服务器进行采样，colocate 模式在程序内部署 vLLM。使用server端时，
- vllm_mode server 参数
  - vllm_server_base_url: vLLM server的Base URL(比如 http://local_host:8000), 默认为None。设置后，忽略host和port设置。
  - vllm_server_host：vLLM server host地址，默认为None，使用外部vLLM server时使用。
  - vllm_server_port vLLM server 服务端口，默认为8000。
  - vllm_server_timeout 连接vLLM server的超时时间，默认为 240s。
  - async_generate: 异步rollout以提高训练速度，注意开启时采样会使用上一轮更新的模型进行采样，不支持多轮场景。默认`false`.
- vllm_mode colocate 参数（更多参数支持参考[vLLM参数](#vLLM参数)。）
  - vllm_gpu_memory_utilization: vllm透传参数，默认为0.9。
  - vllm_max_model_len: vllm透传参数，默认为None。
  - vllm_enforce_eager: vllm透传参数，默认为False。
  - vllm_limit_mm_per_prompt: vllm透传参数，默认为None。
  - vllm_enable_prefix_caching: vllm透传参数，默认为True。
  - sleep_level: 训练时释放 vLLM 显存，可选项为[0, 1], 默认为0，不释放
  - offload_optimizer: 是否在vLLM推理时offload optimizer参数，默认为False。
  - offload_model: 是否在vLLM推理时 offload 模型，默认为False。
  - completion_length_limit_scope: 在多轮对话中，`max_completion_length` 的限制范围。
  `total`限制所有对话轮次的总输出长度不超过`max_completion_length`, `per_round`限制每一轮的输出长度。
- num_iterations: 每个批次代更新次数，默认为1。
- epsilon: clip 系数，默认为0.2。
- epsilon_high: upper clip 系数，默认为None，设置后与epsilon共同构成[epsilon, epsilon_high]裁剪范围。
- delta: [INTELLECT-2 tech report](https://huggingface.co/papers/2505.07291)中双侧 GRPO 上界裁剪值。若设置，建议大于 1 + epsilon。默认为None。
- sync_ref_model: 是否定期同步ref_model，默认为False。
  - ref_model_mixup_alpha: 控制在更新过程中model和先前ref_model之间的混合。更新公式为 $π_{ref} = α * π_θ + (1 - α) * π_{ref_{prev}}$。默认为0.6。
  - ref_model_sync_steps：同步频率，默认为512。
- move_model_batches: 在模型向vLLM等快速推理框架移动参数时，将layers分为多少个batch. 默认为None, 代表整个模型不进行拆分，否则拆分为move_model_batches+1(非layer参数)+1(多模态部分参数)个。注意：该参数仅对LoRA(PEFT)训练有意义。
- multi_turn_scheduler: 多轮GRPO参数, 传入对应的plugin名称, 同时在plugin/multi_turn.py中添加好对应的实现。
- max_turns: 多轮GRPO的轮数上限。默认为None，不做限制。
- dynamic_sample：筛除group内奖励标准差为0的数据，额外采样新数据，默认为False。
- max_resample_times：dynamic_sample设置下限制重采样次数，默认3次。
- overlong_filter：跳过超长截断的样本，不参与loss计算，默认为False。
- top_entropy_quantile: 仅对熵值处于前指定分位的 token 参与损失计算，默认为1.0，即不过滤低熵 token，具体参考[文档](./GRPO/AdvancedResearch/entropy_mask.md)
- log_entropy: 记录训练中的熵值变化动态，默认为False，具体参考[文档](./GRPO/GetStarted/GRPO.md#logged-metrics)
- importance_sampling_level: 控制重要性采样比计算，可选项为 `token` 和 `sequence`，`token` 模式下保留原始的每个 token 的对数概率比，`sequence` 模式下则会对序列中所有有效 token 的对数概率比进行平均。[GSPO论文](https://www.arxiv.org/abs/2507.18071)中使用sequence级别计算来稳定训练，默认为`token`。

cosine 奖励参数
- cosine_min_len_value_wrong：cosine 奖励函数参数，生成错误答案时，最小长度对应的奖励值。默认值为-0.5。
- cosine_max_len_value_wrong：生成错误答案时，最大长度对应的奖励值。默认值为0.0。
- cosine_min_len_value_correct：生成正确答案时，最小长度对应的奖励值。默认值为1.0。
- cosine_max_len_value_correct：生成正确答案时，最大长度对应的奖励值。默认值为0.5。
- cosine_max_len：生成文本的最大长度限制。默认等于 max_completion_length。

repetition 奖励参数
- repetition_n_grams：用于检测重复的 n-gram 大小。默认值为3。
- repetition_max_penalty：最大惩罚值，用于控制惩罚的强度。默认值为-1.0。

soft overlong 奖励参数
- soft_max_length: 论文中的L_max，模型的最大生成长度，默认等于max_completion_length。
- soft_cache_length: 论文中的L_cache，控制长度惩罚区间，区间为[soft_max_length-soft_cache_length, soft_max_length]。

### 推理参数

推理参数除包含[基本参数](#基本参数)、[合并参数](#合并参数)、[vLLM参数](#vllm参数)、[LMDeploy参数](#LMDeploy参数)外，还包含下面的部分：

- 🔥infer_backend: 推理加速后端，支持'pt'、'vllm'、'sglang'、'lmdeploy'四种推理引擎。默认为'pt'。
- 🔥max_batch_size: 指定infer_backend为pt时生效，用于批量推理，默认为1。若设置为-1，则不受限制。
- 🔥result_path: 推理结果存储路径（jsonl），默认为None，保存在checkpoint目录（含args.json文件）或者'./result'目录，最终存储路径会在命令行中打印。
  - 注意：若已存在`result_path`文件，则会进行追加写入。
- write_batch_size: 结果写入`result_path`的batch_size。默认为1000。若设置为-1，则不受限制。
- metric: 对推理的结果进行评估，目前支持'acc'和'rouge'。默认为None，即不进行评估。
- val_dataset_sample: 推理数据集采样数，默认为None。


### 部署参数

部署参数继承于[推理参数](#推理参数)。

- host: 服务host，默认为'0.0.0.0'。
- port: 端口号，默认为8000。
- api_key: 访问需要使用的api_key，默认为None。
- owned_by: 默认为`swift`。
- 🔥served_model_name: 提供服务的模型名称，默认使用model的后缀。
- verbose: 打印详细日志，默认为True。
  - 注意：在`swift app`或者`swift eval`时，默认为False。
- log_interval: tokens/s统计值打印间隔，默认20秒。设置为-1则不打印。
- max_logprobs: 最多返回客户端的logprobs数量，默认为20。
- Rollout 参数
  - multi_turn_scheduler: 多轮GRPO参数, 传入对应的plugin名称, 同时在plugin/multi_turn.py中添加好对应的实现。
  - max_turns: 多轮GRPO的轮数上限。默认为None，不做限制。

### Rollout参数
Rollout参数继承于[部署参数](#部署参数)
- multi_turn_scheduler: 多轮训练规划器，默认为None，具体参考[文档](./GRPO/DeveloperGuide/多轮训练.md)
- max_turns: 多轮训练下的最大轮数，默认为None，即不做约束。

### Web-UI参数
- server_name: web-ui的host，默认为'0.0.0.0'。
- server_port: web-ui的port，默认为7860。
- share: 默认为False。
- lang: web-ui的语言，可选为'zh', 'en'。默认为'zh'。


### App参数

App参数继承于[部署参数](#部署参数), [Web-UI参数](#Web-UI参数)。
- base_url: 模型部署的base_url，例如`http://localhost:8000/v1`。默认为`None`，使用本地部署。
- studio_title: studio的标题。默认为None，设置为模型名。
- is_multimodal: 是否启动多模态版本的app。默认为None，自动根据model判断，若无法判断，设置为False。
- lang: 覆盖Web-UI参数，默认为'en'。

### 评测参数

评测参数继承于[部署参数](#部署参数)。

- 🔥eval_backend: 评测后端，默认为'Native'，也可以指定为'OpenCompass'或'VLMEvalKit'。
- 🔥eval_dataset: 评测数据集，请查看[评测文档](./评测.md)。
- eval_limit: 每个评测集的采样数，默认为None。
- eval_output_dir: 评测存储结果的文件夹，默认为'eval_output'。
- temperature: 覆盖生成参数，默认为0。
- eval_num_proc: 评测时客户端最大并发数，默认为16。
- eval_url: 评测url，例如`http://localhost:8000/v1`。例子可以查看[这里](https://github.com/modelscope/ms-swift/tree/main/examples/eval/eval_url)。默认为None，采用本地部署评估。
- eval_generation_config: 评测时模型推理配置，需传入json字符串格式，例如：`'{"max_new_tokens": 512}'`；默认为None。
- extra_eval_args: 额外评测参数，需传入json字符串格式，默认为空。仅对Native评测有效，更多参数说明请查看[这里](https://evalscope.readthedocs.io/zh-cn/latest/get_started/parameters.html)
- local_dataset: 部分评测集，如`CMB`无法直接运行，需要下载额外数据包才可以使用。设置本参数为`true`可以自动下载全量数据包，并在当前目录下创建`data`文件夹并开始评测。数据包仅会下载一次，后续会使用缓存。该参数默认为`false`。
  - 注意：默认评测会使用`~/.cache/opencompass`下的数据集，在指定本参数后会直接使用当前目录下的data文件夹。

### 导出参数

导出参数除包含[基本参数](#基本参数)和[合并参数](#合并参数)外，还包含下面的部分:

- 🔥output_dir: 导出结果存储路径。默认为None，会自动设置合适后缀的路径。
- exist_ok: 如果output_dir存在，不抛出异常，进行覆盖。默认为False。
- 🔥quant_method: 可选为'gptq'、'awq'、'bnb'和'fp8'，默认为None。例子参考[这里](https://github.com/modelscope/ms-swift/tree/main/examples/export/quantize)。
- quant_n_samples: gptq/awq的校验集采样数，默认为256。
- max_length: 校准集的max_length, 默认值2048。
- quant_batch_size: 量化batch_size，默认为1。
- group_size: 量化group大小，默认为128。
- to_cached_dataset: 提前对数据集进行tokenize并导出，默认为False。例子参考[这里](https://github.com/modelscope/ms-swift/tree/main/examples/export/cached_dataset)。
  - 注意：数据packing在训练时进行，而不在此处。
- to_ollama: 产生ollama所需的Modelfile文件。默认为False。
- 🔥to_mcore: HF格式权重转成Megatron格式。默认为False。
- to_hf: Megatron格式权重转成HF格式。默认为False。
- mcore_model: mcore格式模型路径。默认为None。
- thread_count: `--to_mcore true`时的模型切片数。默认为None，根据模型大小自动设置，使得最大分片小于10GB。
- 🔥test_convert_precision: 测试HF和Megatron格式权重转换的精度误差。默认为False。
- 🔥push_to_hub: 是否推送hub，默认为False。例子参考[这里](https://github.com/modelscope/ms-swift/blob/main/examples/export/push_to_hub.sh)。
- hub_model_id: 推送的model_id，默认为None。
- hub_private_repo: 是否是private repo，默认为False。
- commit_message: 提交信息，默认为'update files'。

### 采样参数

- prm_model: 过程奖励模型的类型，可以是模型id（以pt方式拉起），或者plugin中定义的prm key（自定义推理过程）。
- orm_model: 结果奖励模型的类型，通常是通配符或测试用例等，一般定义在plugin中。
- sampler_type：采样类型，目前支持 sample, mcts，未来会支持 dvts。
- sampler_engine：支持`pt`, `lmdeploy`, `vllm`, `client`, `no`，默认为`pt`，采样模型的推理引擎。
- sampler_type：采样类型，目前支持sample（do_sample方式），未来会支持mcts和dvts。
- sampler_engine：支持`pt`, `lmdeploy`, `vllm`, `no`，默认为`pt`，采样模型的推理引擎。
- output_dir：输出目录，默认为`sample_output`。
- output_file：输出文件名称，默认为`None`使用时间戳作为文件名。传入时不需要传入目录，仅支持jsonl格式。
- override_exist_file：如`output_file`存在，是否覆盖。
- num_sampling_per_gpu_batch_size：每次采样的batch_size。
- num_sampling_per_gpu_batches：共采样多少batch。
- n_best_to_keep：返回多少最佳sequences。
- data_range：本采样处理数据集的分片。传入格式为`2 3`，代表数据集分为3份处理（这意味着通常有三个`swift sample`在并行处理），本实例正在处理第3个分片。
- temperature：在这里默认为1.0。
- prm_threshold：PRM阈值，低于该阈值的结果会被过滤掉，默认值为`0`。
- easy_query_threshold：单个query的所有采样中，ORM评估如果正确，大于该比例的query会被丢弃，防止过于简单的query出现在结果中，默认为`None`，代表不过滤。
- engine_kwargs：传入sampler_engine的额外参数，以json string传入，例如`{"cache_max_entry_count":0.7}`。
- num_return_sequences：采样返回的原始sequence数量。默认为64，本参数对`sample`采样有效。
- cache_files：为避免同时加载prm和generator造成显存OOM，可以分两步进行采样，第一步将prm和orm置为`None`，则所有结果都会输出到文件中，第二次运行采样将sampler_engine置为`no`并传入`--cache_files`为上次采样的输出文件，则会使用上次输出的结果进行prm和orm评估并输出最终结果。
  - 注意：使用cache_files时，`--dataset`仍然需要传入，这是因为cache_files的id是由原始数据计算的md5，需要把两部分信息结合使用。

#### MCTS
- rollout_depth：rollout 时的最大深度，默认为 `5`。
- rollout_start_depth：开始 rollout 时的深度，低于此深度的节点只会进行 expand 操作，默认为 `3`。
- max_iterations：mcts 的最大迭代次数，默认为 `100`。
- process_reward_rate：select 中计算 value 时 process reward 占的比例，默认为 `0.0`，即不使用 PRM。
- exploration_rate：UCT 算法中的探索参数，值越大越照顾探索次数较小的节点，默认为 `0.5`。
- api_key：使用 client 作为推理引擎时需要，默认为 `EMPTY`。
- base_url：使用 client 作为推理引擎时需要，默认为 'https://dashscope.aliyuncs.com/compatible-mode/v1'


## 特定模型参数
特定模型参数可以通过`--model_kwargs`或者环境变量进行设置，例如: `--model_kwargs '{"fps_max_frames": 12}'`或者`FPS_MAX_FRAMES=12`。

### qwen2_vl, qvq, qwen2_5_vl, mimo_vl, keye_vl
参数含义同`qwen_vl_utils`或者`qwen_omni_utils`库，可以查看[这里](https://github.com/QwenLM/Qwen2.5-VL/blob/main/qwen-vl-utils/src/qwen_vl_utils/vision_process.py#L24)。

- IMAGE_FACTOR: 默认为28。
- MIN_PIXELS: 默认为`4 * 28 * 28`。
- 🔥MAX_PIXELS: 默认为`16384 * 28 * 28`，参考[这里](https://github.com/modelscope/ms-swift/blob/main/examples/train/multimodal/ocr.sh#L3)。
- MAX_RATIO: 默认为200。
- VIDEO_MIN_PIXELS: 默认为`128 * 28 * 28`。
- 🔥VIDEO_MAX_PIXELS: 默认为`768 * 28 * 28`，参考[这里](https://github.com/modelscope/ms-swift/blob/main/examples/train/multimodal/video.sh#L7)。
- VIDEO_TOTAL_PIXELS: 默认为`24576 * 28 * 28`。
- FRAME_FACTOR: 默认为2。
- FPS: 默认为2.0。
- FPS_MIN_FRAMES: 默认为4。
- 🔥FPS_MAX_FRAMES: 默认为768，参考[这里](https://github.com/modelscope/ms-swift/blob/main/examples/train/multimodal/video.sh#L8)。

### qwen2_audio
- SAMPLING_RATE: 默认为16000。

### qwen2_5_omni
qwen2_5_omni除了包含qwen2_5_vl和qwen2_audio的模型特定参数外，还包含以下参数：
- USE_AUDIO_IN_VIDEO: 默认为False。
- 🔥ENABLE_AUDIO_OUTPUT: 默认为True。若使用zero3进行训练，请设置为False。

### internvl, internvl_phi3
参数含义可以查看[这里](https://modelscope.cn/models/OpenGVLab/Mini-InternVL-Chat-2B-V1-5)。
- MAX_NUM: 默认为12。
- INPUT_SIZE: 默认为448。

### internvl2, internvl2_phi3, internvl2_5, internvl3
参数含义可以查看[这里](https://modelscope.cn/models/OpenGVLab/InternVL2_5-2B)。
- MAX_NUM: 默认为12。
- INPUT_SIZE: 默认为448。
- VIDEO_MAX_NUM: 默认为1。视频的MAX_NUM。
- VIDEO_SEGMENTS: 默认为8。


### minicpmv2_6, minicpmo2_6
- MAX_SLICE_NUMS: 默认为9，参考[这里](https://modelscope.cn/models/OpenBMB/MiniCPM-V-2_6/file/view/master?fileName=config.json&status=1)。
- VIDEO_MAX_SLICE_NUMS: 默认为1，视频的MAX_SLICE_NUMS，参考[这里](https://modelscope.cn/models/OpenBMB/MiniCPM-V-2_6)。
- MAX_NUM_FRAMES: 默认为64，参考[这里](https://modelscope.cn/models/OpenBMB/MiniCPM-V-2_6)。

### minicpmo2_6
- INIT_TTS: 默认为False。
- INIT_AUDIO: 默认为False。

### ovis1_6, ovis2
- MAX_PARTITION: 默认为9，参考[这里](https://github.com/AIDC-AI/Ovis/blob/d248e34d755a95d24315c40e2489750a869c5dbc/ovis/model/modeling_ovis.py#L312)。

### mplug_owl3, mplug_owl3_241101
- MAX_NUM_FRAMES: 默认为16，参考[这里](https://modelscope.cn/models/iic/mPLUG-Owl3-7B-240728)。

### xcomposer2_4khd
- HD_NUM: 默认为55，参考[这里](https://modelscope.cn/models/Shanghai_AI_Laboratory/internlm-xcomposer2-4khd-7b)。

### xcomposer2_5
- HD_NUM: 图片数量为1时，默认值为24。大于1，默认为6。参考[这里](https://modelscope.cn/models/AI-ModelScope/internlm-xcomposer2d5-7b/file/view/master?fileName=modeling_internlm_xcomposer2.py&status=1#L254)。

### video_cogvlm2
- NUM_FRAMES: 默认为24，参考[这里](https://github.com/THUDM/CogVLM2/blob/main/video_demo/inference.py#L22)。

### phi3_vision
- NUM_CROPS: 默认为4，参考[这里](https://modelscope.cn/models/LLM-Research/Phi-3.5-vision-instruct)。

### llama3_1_omni
- N_MELS: 默认为128，参考[这里](https://github.com/ictnlp/LLaMA-Omni/blob/544d0ff3de8817fdcbc5192941a11cf4a72cbf2b/omni_speech/infer/infer.py#L57)。

### video_llava
- NUM_FRAMES: 默认为16。


## 其他环境变量
- CUDA_VISIBLE_DEVICES: 控制使用哪些GPU卡。默认使用所有卡。
- ASCEND_RT_VISIBLE_DEVICES: 控制使用哪些NPU卡（ASCEND卡生效）。默认使用所有卡。
- MODELSCOPE_CACHE: 控制缓存路径。
- PYTORCH_CUDA_ALLOC_CONF: 推荐设置为`'expandable_segments:True'`，这将减少GPU内存碎片，具体请参考[torch文档](https://docs.pytorch.org/docs/stable/notes/cuda.html#cuda-memory-management)。
- NPROC_PER_NODE: torchrun中`--nproc_per_node`的参数透传。默认为1。若设置了`NPROC_PER_NODE`或者`NNODES`环境变量，则使用torchrun启动训练或推理。
- MASTER_PORT: torchrun中`--master_port`的参数透传。默认为29500。
- MASTER_ADDR: torchrun中`--master_addr`的参数透传。
- NNODES: torchrun中`--nnodes`的参数透传。
- NODE_RANK: torchrun中`--node_rank`的参数透传。
- LOG_LEVEL: 日志的level，默认为'INFO'，你可以设置为'WARNING', 'ERROR'等。
- SWIFT_DEBUG: 在`engine.infer(...)`时，若设置为'1'，则会打印input_ids和generate_ids的内容。
- VLLM_USE_V1: 用于切换vLLM使用V0/V1版本。
